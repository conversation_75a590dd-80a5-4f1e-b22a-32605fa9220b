<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jky.digital.multiuser.mapper.DigitalSmzProjectInfoMapper">


    <select id="queryByUserPhoneAndId" resultType="com.jky.digital.multiuser.entity.DigitalSmzProjectInfo">
        select distinct p.* from smz_project_info p left join tb_user_project up on p.id = up.project_id
        left join smz_user u on up.user_id = u.id
        <where>
            u.id = #{id}
        </where>
    </select>
</mapper>