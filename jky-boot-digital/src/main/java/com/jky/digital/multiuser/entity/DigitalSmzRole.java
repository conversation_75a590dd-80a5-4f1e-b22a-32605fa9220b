package com.jky.digital.multiuser.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * @description:
 * @author: cq
 * @date: 2025/6/9 17:23
 */
@Data
@TableName("smz_role")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "smz_role对象", description = "角色表")
public class DigitalSmzRole {

        /**
         * ID主键
         */
        @TableId(type = IdType.ASSIGN_ID)
        private String id;

        /**
         * 角色名称
         */
        private String name;

        /**
         * 角色描述
         */
        private String description;

        /**
         * 角色类型：1业主，2非业主
         */
        private Integer type;

        /**
         * 状态：0启用，1禁用
         */
        private Integer status;

        /**
         * 排序
         */
        private Double orders;

        /**
         * 创建时间
         */
        private Date createTime;

        /**
         * 创建人用户id
         */
        private String createUserId;

        /**
         * 更新人用户id
         */
        private String updateUserId;

        /**
         * 更新时间
         */
        private Date updateTime;

        /**
         * 角色编号
         */
        private String code;

        /**
         * 租户ID
         */
        private String tenantId;

        /**
         * 数据来源(初始化 init 迁移 migrate 页面添加 page)
         */
        private String dataSource;

        /**
         * 删除标识（0正常，1删除）
         */
        private Integer delFlag;

        /**
         * 项目id
         */
        private String projectId;

        /**
         * 角色数据权限类型（1 只能看自己,2 只能看当前组织,3 能看当前组织及以下组织，4 不控权）
         */
        private Integer authType;

        /**
         * 管辖区域,多个逗号分割
         */
        private String jurisdictionArea;

        /**
         * 是否是区域管理角色 0否 1是
         */
        private Integer isArea;

}

