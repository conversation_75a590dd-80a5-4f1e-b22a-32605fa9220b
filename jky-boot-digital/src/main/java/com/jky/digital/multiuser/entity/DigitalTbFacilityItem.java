package com.jky.digital.multiuser.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * @description:
 * @author: cq
 * @date: 2025/6/9 17:36
 */
@Data
@TableName("tb_facility_item")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = " tb_facility_item 对象", description = "基础设备详情")
public class DigitalTbFacilityItem {
    /**
     * id(雪花)
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 所属项目
     */
    private String projectId;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 设备简称
     */
    private String deviceAlisename;

    /**
     * 设备唯一序列号
     */
    private String sn;

    /**
     * 设备合格证有效期
     */
    private Date equipmentValidityDate;

    /**
     * 出厂年月
     */
    private Date leaveFactoryDate;

    /**
     * 设备状态 0:离线, 1:在线
     */
    private String status;

    /**
     * 设备图标 （url）
     */
    private String icon;

    /**
     * 维保时间
     */
    private Date maintainTime;

    /**
     * 到期时间
     */
    private Date expireTime;

    /**
     * 设备mac地址
     */
    private String mac;

    /**
     * 设备高清地址
     */
    private String macHd;

    /**
     * [摄像头专用]是否全景视频，0否，1是
     */
    private Integer isPanorama;

    /**
     * [摄像头专用]视频用途
     */
    private String videoUse;

    /**
     * 设备平面图图片地址[专用:深基坑/高支模/起重机]
     */
    private String imgUrl;

    /**
     * 设备在平面图上的坐标[专用:深基坑/高支模/起重机]
     */
    private String coordinate;

    /**
     * 设备类型
     */
    private String deviceType;

    /**
     * 厂家名称
     */
    private String manufacturersName;

    /**
     * 逻辑删除 （0 正常，1删除）
     */
    private Integer delFlag;

    /**
     * 数据来源
     */
    private String dataSource;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 更新人名称
     */
    private String updateName;

    /**
     * 创建人名称
     */
    private String createName;

    /**
     * 创建人ID
     */
    private String createId;

    /**
     * 更新人ID
     */
    private String updateId;

    /**
     * 租户ID
     */
    private String tenantId;

    /**
     * 排序字段
     */
    private Integer sort;

    /**
     * 最新上传经度坐标
     */
    private Double longitude;

    /**
     * 最新上传纬度坐标
     */
    private Double latitude;

    /**
     * 是否抽帧 0否 1是
     */
    private Integer isScreenShot;

    /**
     * 视频设备编码
     */
    private String deviceSerial;

    /**
     * 视频通道号
     */
    private String channelNo;

    /**
     * 摄像头类型 1枪机类 2球机类
     */
    private Integer cameraType;

    /**
     * 设备状态更新时间
     */
    private Date statusUpdateTime;

    /**
     * 设备分区 1作业区 2办公区 3生活区
     */
    private Integer zone;

    /**
     * 视频平台 1数字工地 2天影平台
     */
    private Integer platform;

    /**
     * 是否支持云台控制 1支持 0不支持
     */
    private Integer isCanControl;

    /**
     * 摄像机用途：执法点、监控点
     */
    private String lable;
}

