package com.jky.digital.multiuser.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * @description:
 * @author: cq
 * @date: 2025/6/9 17:29
 */
@Data
@TableName("view_gdh5_smz_user")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = " 对象", description = "")
public class DigitalSmzUser {


        /**
         * ID主键
         */
        @TableId(type = IdType.ASSIGN_ID)
        private String id;

        /**
         * 身份证编号
         */
        private String idCardNum;

        /**
         * 电话
         */
        private String phone;

        /**
         * 是否认证：0未认证 1 已认证 2 认证中 3认证失败
         */
        private Integer certified;

        /**
         * 认证类型：1大白认证 2人工认证
         */
        private Integer certifiedType;

        /**
         * 用户类别：1业主人员 2非业主人员
         */
        private Integer userType;

        /**
         * 工作状态：0在职、1离职
         */
        private Integer workStatus;

        /**
         * 是否签订劳动合同：0未签订合同、1已签订合同
         */
        private Integer isSign;

        /**
         * (疫情)是否为重点关注人员：0不是重点、1重点
         */
        private Integer isPoint;

        /**
         * 是否黑名单：0否 1是
         */
        private Integer isBlacklist;

        /**
         * 删除标识（0正常，1删除）
         */
        private Integer delFlag;

        /**
         * 认证时间
         */
        private Date certifiedTime;

        /**
         * 备注
         */
        private String remark;

        /**
         * 排序值
         */
        private Integer orders;

        /**
         * 创建人用户id
         */
        private String createUserId;

        /**
         * 创建时间
         */
        private Date createTime;

        /**
         * 更新人用户id
         */
        private String updateUserId;

        /**
         * 更新时间
         */
        private Date updateTime;

        /**
         * 数据来源(初始化 init 迁移 migrate 页面添加 page)
         */
        private String dataSource;

        /**
         * 头像 （url）
         */
        private String header;

        /**
         * 民族
         */
        private String nation;

        /**
         * 出生日期
         */
        private Date birth;

        /**
         * 年龄
         */
        private Integer age;

        /**
         * 住址
         */
        private String address;

        /**
         * 性别 0：男 1 ：女
         */
        private Integer sex;

        /**
         * 姓名
         */
        private String realname;

        /**
         * 工种类别ID(来自于字典)
         */
        private String jobCategoryId;

        /**
         * 工种类别名称
         */
        private String jobCategoryName;

        /**
         * 工种ID
         */
        private String jobWorkRelationId;

        /**
         * 工种名称
         */
        private String professionName;

        /**
         * 所属组织
         */
        private Long organization;

        /**
         * 组织名称
         */
        private String organizationName;

        /**
         * 所属企业
         */
        private Long company;

        /**
         * 企业名称
         */
        private String companyName;

        /**
         * 班组
         */
        private String classes;

        /**
         * 手环设备号
         */
        private String braceletNum;

        /**
         * 手环绑定时间
         */
        private Date braceletBindingDate;

        /**
         * 智能安全帽设备号
         */
        private String helmetNum;

        /**
         * 智能安全帽绑定时间
         */
        private Date helmetBindingDate;

        /**
         * 进场状态：0已进场，1已退场
         */
        private Integer isExit;

        /**
         * 合同编码
         */
        private String contractCode;

        /**
         * 合同文件附件id
         */
        private String contractFileRelationId;

        /**
         * 合同文件附件url
         */
        private String contractFileUrl;

        /**
         * 合同登记状态
         */
        private String contractState;

        /**
         * 合同有效期
         */
        private Integer contractValidityDate;

        /**
         * 合同起止日期
         */
        private String contractStartEnd;

        /**
         * 工作时长
         */
        private Integer workDuration;

        /**
         * 工作内容
         */
        private String workContent;

        /**
         * 劳动报酬
         */
        private String salay;

        /**
         * 工资卡号
         */
        private String salayCardNum;

        /**
         * 备注
         */
        private String content;

        /**
         * 租户ID
         */
        private String tenantId;

        /**
         * 员工ID
         */
        private String staffId;

        /**
         * 审核状态：0-审核不通过 1待审核 9-正常
         */
        private Integer approvalStatus;

        /**
         * 身份证照片url
         */
        private String idCardPhotoUrl;

        /**
         * 文件存储类型：天翼云oss：tyyoss,本地文件服务：fdfs
         */
        private String fileStoreType;

}

