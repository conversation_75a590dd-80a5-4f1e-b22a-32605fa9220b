package com.jky.digital.multiuser.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Date;

/**
 * @description:
 * @author: cq
 * @date: 2025/6/9 17:19
 */
@Data
@TableName("smz_project_info")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "smz_project_info对象", description = "项目表")
public class DigitalSmzProjectInfo {
        /**
         * ID主键
         */
        @TableId(type = IdType.ASSIGN_ID)
        private String id;

        /**
         * 序号
         */
        private Integer numerical;

        /**
         * 项目名称
         */
        private String projectName;

        /**
         * 项目编号
         */
        private String projectCode;

        /**
         * 组织ID
         */
        private String orgId;

        /**
         * 所属名称
         */
        private String company;

        /**
         * 建设单位
         */
        private String managementUnit;

        /**
         * 设计单位
         */
        private String designUnit;

        /**
         * 项目状态 0停工、1在建、2完工、3待开工
         */
        private Integer projectStatus;

        /**
         * 承建单位
         */
        private String constructionUnit;

        /**
         * 总建筑面积
         */
        private String floorArea;

        /**
         * 主管单位
         */
        private String chargeUnit;

        /**
         * 项目管理单位
         */
        private String projectManagerUnit;

        /**
         * 监理单位
         */
        private String supervisorUnit;

        /**
         * 施工单位
         */
        private String roadworkUnit;

        /**
         * 勘察单位
         */
        private String surveyUnit;

        /**
         * 计划开工日期
         */
        private Date startDate;

        /**
         * 计划竣工日期
         */
        private Date endDate;

        /**
         * 项目经理
         */
        private String projectManager;

        /**
         * 项目地址
         */
        private String projectAddress;

        /**
         * 逻辑删除 （0 正常，1删除）
         */
        private Integer delFlag;

        /**
         * 数据来源
         */
        private String dataSource;

        /**
         * 创建时间
         */
        private Timestamp createTime;

        /**
         * 更新时间
         */
        private Timestamp updateTime;

        /**
         * 更新人名称
         */
        private String updateName;

        /**
         * 创建人名称
         */
        private String createName;

        /**
         * 创建人ID
         */
        private String createId;

        /**
         * 更新人ID
         */
        private String updateId;

        /**
         * 租户ID
         */
        private String tenantId;

        private String organizationId;

        private String organizationName;

        /**
         * 项目图片url
         */
        private String projectPicUrl;

        /**
         * 经纬度
         */
        private String longAndLat;

        /**
         * 项目概述
         */
        private String projectBasic;

        /**
         * 项目规模
         */
        private String projectScale;

        /**
         * 参见信息
         */
        private String participationInfo;

        /**
         * 项目路由
         */
        private String route;

        /**
         * 文件存储类型：天翼云oss：tyyoss,本地文件服务：fdfs
         */
        private String fileStoreType;

        /**
         * 纬度
         */
        private BigDecimal latitude;

        /**
         * 经度
         */
        private BigDecimal longitude;

        /**
         * 关联资料模版ID
         */
        private String archiveTemplateId;

        /**
         * 实景图url
         */
        private String realisticPicture;

        /**
         * 实景图点位json
         */
        private String realisticDotJson;

        /**
         * 单体项目库项目id
         */
        private String monomerProjectId;

        /**
         * 所属区域
         */
        private String belongArea;

        /**
         * 更多实景图url 多个逗号分割
         */
        private String moreRealisticPictures;

}

