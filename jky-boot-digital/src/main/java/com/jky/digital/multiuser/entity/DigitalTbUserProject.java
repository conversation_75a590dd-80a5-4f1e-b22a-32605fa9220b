package com.jky.digital.multiuser.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * @description:
 * @author: cq
 * @date: 2025/6/9 17:38
 */
@Data
@TableName("view_gdh5_tb_user_project")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = " tb_user_project对象", description = "人员项目关系表")
public class DigitalTbUserProject {
    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 用户id
     */
    private String userId;

    /**
     * 项目id
     */
    private String projectId;

    /**
     * 当前是否已绑定 0未绑定 1已绑定
     */
    private Integer status;

    /**
     * 组织id
     */
    private String organization;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}

