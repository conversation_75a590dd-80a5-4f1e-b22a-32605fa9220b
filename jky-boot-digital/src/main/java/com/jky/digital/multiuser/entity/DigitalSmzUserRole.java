package com.jky.digital.multiuser.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @function:
 * @auther: cq
 * @date: 2025/6/9
 */
@Data
@TableName("smz_user_role")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = " smz_user_role 对象", description = "")
public class DigitalSmzUserRole {

    /**
     * ID主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 角色ID
     */
    private String roleId;

    /**
     * 租户ID
     */
    private String tenantId;

    /**
     * 数据来源(初始化 init 迁移 migrate 页面添加 page)
     */
    private String dataSource;

    /**
     * 删除标识（0正常，1删除）
     */
    private Integer delFlag;

    /**
     * 项目id
     */
    private String projectId;
}
