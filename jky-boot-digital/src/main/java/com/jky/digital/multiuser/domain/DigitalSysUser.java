package java.com.jky.digital.multiuser.domain;

import com.baomidou.mybatisplus.annotation.TableLogic;
import lombok.Data;

/**
 * @description:
 * @author: cq
 * @date: 2025/6/10 17:25
 */
@Data
public class DigitalSysUser {

    private String username;

    /**
     * 真实姓名
     */
    private String realname;
    /**
     * 性别（1：男 2：女）
     */
    private Integer sex;

    /**
     * 电话
     */
    private String phone;

    /**
     * 部门code(当前选择登录部门)
     */
    private String orgCode;

    /**
     * 部门名称
     */
    private transient String orgCodeTxt;

    /**
     * 状态(1：正常  2：冻结 ）
     */
    private Integer status;

    /**
     * 删除状态（0，正常，1已删除）
     */
    @TableLogic
    private Integer delFlag;

    /**
     * 职务，关联职务表
     */
    private String post;
}

