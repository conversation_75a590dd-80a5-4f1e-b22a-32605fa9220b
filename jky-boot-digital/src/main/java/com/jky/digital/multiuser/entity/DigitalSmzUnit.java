package com.jky.digital.multiuser.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;
/**
 * @description:
 * @author: cq
 * @date: 2025/6/9 17:28
 */
@Data
@TableName("smz_unit")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "smz_unit对象", description = "企业表（承建单位）")
public class DigitalSmzUnit {
    /**
     * ID主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 企业名称
     */
    private String unitName;

    /**
     * 企业机构代码
     */
    private String unitCode;

    /**
     * 企业组织状态：0启用、1停用
     */
    private Integer status;

    /**
     * 创建人用户id
     */
    private String createUserId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新人用户id
     */
    private String updateUserId;

    /**
     * 更新人时间
     */
    private Date updateTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 租户ID
     */
    private String tenantId;

    /**
     * 数据来源(初始化 init 迁移 migrate 页面添加 page)
     */
    private String dataSource;

    /**
     * 删除标识（0正常，1删除）
     */
    private Integer delFlag;

    /**
     * 项目id
     */
    private String projectId;

    /**
     * 企业类型 2勘察设计单位、3监理单位、4施工总包单位、5施工分包单位、6其他单位
     */
    private Integer unitType;

}

