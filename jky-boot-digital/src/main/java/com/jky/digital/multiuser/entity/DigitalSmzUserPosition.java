package com.jky.digital.multiuser.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @description:
 * @author: cq
 * @date: 2025/6/9 17:31
 */
@Data
@TableName("smz_user_position")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = " smz_user_position 对象", description = "")
public class DigitalSmzUserPosition {

    /**
     * 主键id
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 工种id
     */
    private String positionId;

    /**
     * 人员id
     */
    private String userId;

    /**
     * 1健康管理员，
     * 2车管员，
     * 3业主管理员，
     * 4监理管理员，
     * 5勘察管理员，
     * 6总包管理员，
     * 7分包管理员，
     * 8其他管理员，
     * 若为空，则暂无类型区别
     */
    private Integer type;
}

