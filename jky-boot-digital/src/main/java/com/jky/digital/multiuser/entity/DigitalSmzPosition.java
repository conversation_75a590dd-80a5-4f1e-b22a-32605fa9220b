package com.jky.digital.multiuser.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * @description:
 * @author: cq
 * @date: 2025/6/9 17:10
 */
@Data
@TableName("smz_position")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "smz_position对象", description = "职务表")
public class DigitalSmzPosition {

        /**
         * ID主键
         */
        @TableId(type = IdType.ASSIGN_ID)
        private String id;

        /**
         * 岗位名称
         */
        private String name;

        /**
         * 岗位编码(预留)
         */
        private String code;

        /**
         * 岗位类型：1业主、2非业主
         */
        private Integer type;

        /**
         * 岗位类别 1业主，2勘察设计、3监理、4施工总包、5施工分包、6其他人员
         */
        private Integer posiType;

        /**
         * 状态: 0启用、1禁用
         */
        private String status;

        /**
         * 创建时间
         */
        private Date createTime;

        /**
         * 创建人用户id
         */
        private String createUserId;

        /**
         * 更新人用户id
         */
        private String updateUserId;

        /**
         * 更新时间
         */
        private Date updateTime;

        /**
         * 备注
         */
        private String remark;

        /**
         * 排序
         */
        private Integer orders;

        /**
         * 租户ID（已废弃）
         */
        private String tenantId;

        /**
         * 数据来源(初始化 init 迁移 migrate 页面添加 page)
         */
        private String dataSource;

        /**
         * 删除标识（0正常，1删除）
         */
        private Integer delFlag;

        /**
         * 是否是重点岗位（0否，1是）
         */
        private Integer isImportant;

        /**
         * 周应出勤次数
         */
        private Integer weekRequiredAttendanceNum;

        /**
         * 项目ID
         */
        private String projectId;

        /**
         * 是否是劳务工种（0否，1是）
         */
        private Integer isWorker;
}

