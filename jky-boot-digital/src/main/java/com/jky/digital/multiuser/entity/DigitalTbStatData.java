package com.jky.digital.multiuser.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * @description:
 * @author: cq
 * @date: 2025/6/9 17:37
 */
@Data
@TableName("tb_stat_data")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = " tb_stat_data对象", description = "")
public class DigitalTbStatData {
    /**
     * 主键ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 编码
     */
    private String itemCode;

    /**
     * 项目id
     */
    private String projectId;

    /**
     * 数据
     */
    private String itemData;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 接口同步时间
     */
    private Date jkSyncTime;

    /**
     * 备注
     */
    private String remark;
}

