package com.jky.digital.law.util;

import com.itextpdf.html2pdf.ConverterProperties;
import com.itextpdf.html2pdf.HtmlConverter;
import com.itextpdf.html2pdf.resolver.font.DefaultFontProvider;
import com.itextpdf.io.font.FontProgramFactory;
import com.itextpdf.io.font.PdfEncodings;
import com.itextpdf.kernel.font.PdfFont;
import com.itextpdf.kernel.font.PdfFontFactory;
import com.itextpdf.kernel.geom.PageSize;
import com.itextpdf.kernel.pdf.PdfDocument;
import com.itextpdf.kernel.pdf.PdfWriter;
import com.itextpdf.layout.font.FontProvider;
import lombok.extern.slf4j.Slf4j;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @Author: lpg
 * @Date: 2025/06/25
 * @Description:
 */
@Slf4j
public class PdfUtil {
    /**
     * html转pdf
     */
    public static ByteArrayOutputStream convertHtmlToPdf(String html) throws IOException {
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        PdfWriter pdfWriter = new PdfWriter(outputStream);
        PdfDocument pdfDocument = new PdfDocument(pdfWriter);
        //设置为A4大小
        pdfDocument.setDefaultPageSize(PageSize.A4);
        //添加水印
//        pdfDocument.addEventHandler(PdfDocumentEvent.END_PAGE, new WaterMarkEventHandler(waterMark));

        //添加中文字体支持
        ConverterProperties properties = new ConverterProperties();

        //        设置字体
        /*PdfFont sysFont = PdfFontFactory.createFont("STSongStd-Light", "UniGB-UCS2-H", false);
        fontProvider.addFont(sysFont.getFontProgram(), "UniGB-UCS2-H");*/

        //添加自定义字体，例如微软雅黑
        /*if (StringUtils.isNotBlank(fontPath)) {
            PdfFont microsoft = PdfFontFactory.createFont(fontPath, PdfEncodings.IDENTITY_H, false);
            fontProvider.addFont(microsoft.getFontProgram(), PdfEncodings.IDENTITY_H);
        }*/
        // 设置中文字体
        FontProvider fontProvider = new DefaultFontProvider(false, false, false);
        //添加自定义字体
        PdfFont songti1 = PdfFontFactory.createFont(FontProgramFactory.createFont("fonts/SongtiSC-Black.ttf"));
        fontProvider.addFont(songti1.getFontProgram(), PdfEncodings.IDENTITY_H);
        PdfFont songti2 = PdfFontFactory.createFont(FontProgramFactory.createFont("fonts/SongtiSC-Bold.ttf"));
        fontProvider.addFont(songti2.getFontProgram(), PdfEncodings.IDENTITY_H);
        PdfFont songti3 = PdfFontFactory.createFont(FontProgramFactory.createFont("fonts/SongtiSC-Light.ttf"));
        fontProvider.addFont(songti3.getFontProgram(), PdfEncodings.IDENTITY_H);
        PdfFont songti4 = PdfFontFactory.createFont(FontProgramFactory.createFont("fonts/SongtiSC-Regular.ttf"));
        fontProvider.addFont(songti4.getFontProgram(), PdfEncodings.IDENTITY_H);


        ConverterProperties converterProps = new ConverterProperties();
        converterProps.setFontProvider(fontProvider);
        properties.setFontProvider(fontProvider);
        HtmlConverter.convertToPdf(readHtml(html), pdfDocument, properties);
        pdfWriter.close();
        pdfDocument.close();

        return outputStream;
    }

    /**
     * 读取HTML ，并查询当中的&nbsp;或类似符号直接替换为空格
     *
     * @param html
     * @return
     */
    private static String readHtml(String html) {
        // 定义一些特殊字符的正则表达式 如：
        String regEx_special = "\\&[a-zA-Z]{1,10};";
        try {
            //判断HTML内容是否具有HTML的特殊字符标记
            Pattern compile = Pattern.compile(regEx_special, Pattern.CASE_INSENSITIVE);
            Matcher matcher = compile.matcher(html);
            return matcher.replaceAll("");
        } catch (Exception e) {
            e.printStackTrace();
            log.error("错误信息：{}", e.getMessage());
            return null;
        }
    }

}
