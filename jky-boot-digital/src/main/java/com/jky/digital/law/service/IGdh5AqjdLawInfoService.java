package com.jky.digital.law.service;

import com.jky.digital.law.domain.vo.LawInfoVo;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * @Author: lpg
 * @Date: 2025/06/24
 * @Description:
 */
public interface IGdh5AqjdLawInfoService {
    /**
     * 查询执法信息
     */
    List<LawInfoVo> selectList(String projectId, String checkType, String startDate, String endDate);

    /**
     * 查看文书PDF
     *
     * @param response
     * @param id
     */
    void view(HttpServletResponse response, String id);
}
