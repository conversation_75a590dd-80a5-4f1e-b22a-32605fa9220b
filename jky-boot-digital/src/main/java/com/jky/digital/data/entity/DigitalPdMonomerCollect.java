package com.jky.digital.data.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * @description:
 * @author: cq
 * @date: 2025/6/10 14:53
 */
@Data
@TableName("pd_monomer_collect")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = " pd_monomer_collect 对象", description = "")
public class DigitalPdMonomerCollect {

    /**
     * 单体工程信息表ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String monomerId;

    /**
     * 单体工程名称
     */
    private String monomerName;

    /**
     * 项目ID 项目名称 项目代码 宗地代码
     */
    private String projectId;

    /**
     * 许可证号
     */
    private String licencemoney;

    /**
     * 施工许可日期
     */
    private Date licencedate;

    /**
     * 工规证号
     */
    private String proPlanCerNo;

    /**
     * 是否竣工验收
     */
    private String isFinish;

    /**
     * 竣工验收日期
     */
    private Date finishDate;

    /**
     * 是否竣工验收备案
     */
    private String isFinishBackup;

    /**
     * 竣工验收备案证书号
     */
    private String finishBackupNo;

    /**
     * 竣工验收备案日期
     */
    private Date finishBackupDate;

    /**
     * 是否终止监督
     */
    private String isStope;

    /**
     * 终止监督日期
     */
    private Date stopeDate;

    /**
     * 镇区名称
     */
    private String townshipName;

    /**
     * 单体工程数据来源
     */
    private String dataSource;

    /**
     * 单体工程数据来源表主键
     */
    private String dataId;

    /**
     * 数据状态
     */
    private String dataState;

    /**
     * 工程状态
     */
    private String gczt;

    /**
     * 工程类别
     */
    private String gclb;

    /**
     * 数据最后更新时间
     */
    private Date updateTime;

    /**
     * 楼栋代码
     */
    private String lddm;

    /**
     * 安全监督登记号
     */
    private String ajNum;

    /**
     * 质量监督登记号
     */
    private String zjNum;

    /**
     * 安监项目监督号
     */
    private String ajProjectNum;

    /**
     * 质监项目监督号
     */
    private String zjProjectNum;

    /**
     * 工程进度 1:基坑阶段、2:地基基础阶段、3:建筑主体阶段、4:装饰装修及设备安装、5或6:完工
     */
    private String projectStage;

    /**
     * 工程项目类型
     */
    private String projectType;

    /**
     * 数据同步时间
     */
    private Date jkSyncTime;
}

