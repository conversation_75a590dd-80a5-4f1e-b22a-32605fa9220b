package com.jky.digital.data.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * @description:
 * @author: cq
 * @date: 2025/6/10 14:56
 */
@Data
@TableName("project_worker_info_1")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = " project_worker_info_1 对象", description = "")
public class DigitalProjectWorkerInfo {
    /**
     * PROJECT_ID
     */
    private String projectId;

    /**
     * ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     * AJ_NUMS
     */
    private String ajNums;

    /**
     * LINK_PHONE
     */
    private String linkPhone;

    /**
     * CONS_SITE_NAME
     */
    private String consSiteName;

    /**
     * TOWNSHIP_NAME
     */
    private String townshipName;

    /**
     * name
     */
    private String name;

    /**
     * ID_CARD_NO
     */
    private String idCardNo;

    /**
     * ENT_NAME
     */
    private String entName;

    /**
     * ENT_TYPE
     */
    private String entType;

    /**
     * TEAM_NAME
     */
    private String teamName;

    /**
     * TEAM_WORK_TYPE
     */
    private Integer teamWorkType;

    /**
     * TEAM_JOB_CLASS
     */
    private Integer teamJobClass;

    /**
     * WORK_JOB
     */
    private String workJob;

    /**
     * IN_OUT_TYPE
     */
    private String inOutType;

    /**
     * IN_TIME
     */
    private Date inTime;

    /**
     * OUT_TIME
     */
    private Date outTime;

    /**
     * 最后更新时间
     */
    private Date updatetime;

    /**
     * 数据全字段MD5
     */
    private String dataMd5;

    /**
     * 数据标识
     */
    private String zzDataFlag;

    /**
     * CONS_SITE_ID
     */
    private String consSiteId;

    /**
     * 照片url
     */
    private String photoPath;

    /**
     * 同步时间
     */
    private Date jkSyncTime;
}

