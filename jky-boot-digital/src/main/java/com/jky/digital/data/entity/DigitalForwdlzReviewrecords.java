package com.jky.digital.data.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * @description:
 * @author: cq
 * @date: 2025/6/10 14:51
 */
@Data
@TableName("forwdlz_reviewrecords")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "forwdlz_reviewrecords 对象", description = "")
public class DigitalForwdlzReviewrecords {


    @TableId(type = IdType.ASSIGN_ID)
    private String rrid;

    /**
     * 审核时间
     */
    private String rrdatetime;

    /**
     * 所有者
     */
    private String owner;

    /**
     * 状态
     */
    private Integer state;

    /**
     * 关联的流程记录ID
     */
    private String rpid;

    /**
     * 关联的项ID
     */
    private String riid;

    /**
     * 项类型
     */
    private String ritype;

    /**
     * 审核人ID
     */
    private String rtid;

    /**
     * 附件ID
     */
    private String rfid;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建时间
     */
    private Date createtime;

    /**
     * 二维码
     */
    private String qrcode;

    /**
     * 数据同步时间
     */
    private Date jkSyncTime;
}

