package com.jky.digital.data.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * @description:
 * @author: cq
 * @date: 2025/6/10 14:55
 */
@Data
@TableName("project_worker_attention_1")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = " project_worker_attention_1 对象", description = "")
public class DigitalProjectWorkerAttention {

    /**
     * PROJECT_ID
     */
    private String projectId;

    /**
     * AJ_NUMS
     */
    private String ajNums;

    /**
     * LINK_PHONE
     */
    private String linkPhone;

    /**
     * CONS_SITE_NAME
     */
    private String consSiteName;

    /**
     * TOWNSHIP_NAME
     */
    private String townshipName;

    /**
     * ID
     */
    private String id;

    /**
     * name
     */
    private String name;

    /**
     * ID_CARD_NO
     */
    private String idCardNo;

    /**
     * ENT_NAME
     */
    private String entName;

    /**
     * TEAM_WORK_TYPE
     */
    private Integer teamWorkType;

    /**
     * TEAM_JOB_CLASS
     */
    private Integer teamJobClass;

    /**
     * TEAM_NAME
     */
    private String teamName;

    /**
     * TEAM_TYPE
     */
    private String teamType;

    /**
     * WORK_JOB
     */
    private String workJob;

    /**
     * IN_OUT_TIME
     */
    private Date inOutTime;

    /**
     * KQ_FILE
     */
    private String kqFile;

    /**
     * DIRECTION
     */
    private String direction;

    /**
     * 最后更新时间
     */
    private Date updatetime;

    /**
     * 数据全字段MD5
     */
    private String dataMd5;

    /**
     * 数据标识
     */
    private String zzDataFlag;

    /**
     * CONS_SITE_ID
     */
    private String consSiteId;

    /**
     * 同步时间
     */
    private Date jkSyncTime;
}

