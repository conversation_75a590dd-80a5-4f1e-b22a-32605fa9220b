package com.jky.digital.data.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * @description:
 * @author: cq
 * @date: 2025/6/10 14:46
 */
@Data
@TableName("dgzjjzj_jcjg_core_sample")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = " dgzjjzj_jcjg_core_sample对象", description = "")
public class DigitalDgzjjzjJcjgCoreSample {
    @TableId(type = IdType.ASSIGN_ID)
    private String archivesInstanceId;
    private String projectMonomerId;
    private String archivesId;
    private String parentId;
    private String archivesNo;
    private String archivesName;
    private String archivesType;
    private String fileType;
    private String fileUrl;
    private Double fileSize;
    private Integer filePage;
    private Date uploadDate;
    private String uploadState;
    private String auditState;
    private String status;
    private String createBy;
    private String updateBy;
    private Date createTime;
    private Date updateTime;
    private String returnBackReason;
    private String returnBackRemark;
    private String cjEntConfirmFile;
}

