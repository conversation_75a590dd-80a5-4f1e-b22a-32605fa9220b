package com.jky.digital.data.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * @description:
 * @author: cq
 * @date: 2025/6/10 14:45
 */
@Data
@TableName("dgdoc_archives_instance")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = " 对象", description = "")
public class DigitalDgdocArchivesInstance {
    /**
     * 档案实例ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String archivesInstanceId;

    /**
     * 项目/单体ID
     */
    private String projectMonomerId;

    /**
     * 档案ID
     */
    private String archivesId;

    /**
     * 档案父级ID
     */
    private String parentId;

    /**
     * 档案编号
     */
    private String archivesNo;

    /**
     * 档案名称
     */
    private String archivesName;

    /**
     * 档案类型 0-项目档案 1-单体档案
     */
    private String archivesType;

    /**
     * 文件类型
     */
    private String fileType;

    /**
     * 文件地址
     */
    private String fileUrl;

    /**
     * 文件大小
     */
    private Double fileSize;

    /**
     * 文件页数
     */
    private Integer filePage;

    /**
     * 上传日期
     */
    private Date uploadDate;

    /**
     * 上传状态 0-未上传 1-部分上传 2-已上传
     */
    private String uploadState;

    /**
     * 审核状态 0-未审核 1-部分审核 2-审核通过 3-已退回
     */
    private String auditState;

    /**
     * 状态 0-正常 1-作废
     */
    private String status;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 更新者
     */
    private String updateBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 退回原因
     */
    private String returnBackReason;

    /**
     * 退回备注
     */
    private String returnBackRemark;

    /**
     * 承建单位确认文件
     */
    private String cjEntConfirmFile;
}

