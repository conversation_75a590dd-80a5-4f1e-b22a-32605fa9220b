package com.jky.digital.data.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @description:
 * @author: cq
 * @date: 2025/6/10 14:57
 */
@Data
@TableName("relate_project")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = " relate_project对象", description = "")
public class DigitalRelateProject {
    /**
     * 项目库id
     */
    private String projectId;

    /**
     * 需要匹配的项目id
     */
    private String matchId;

    /**
     * 类型：wdlz-危大工程，dgem-基坑监测，dggdeim-电子档案，dgzj-数字工地
     */
    private String type;
}

