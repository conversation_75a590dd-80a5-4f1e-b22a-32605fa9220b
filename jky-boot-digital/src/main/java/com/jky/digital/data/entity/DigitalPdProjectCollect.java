package com.jky.digital.data.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * @description:
 * @author: cq
 * @date: 2025/6/10 14:54
 */
@Data
@TableName("pd_project_collect")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = " pd_project_collect对象", description = "")
public class DigitalPdProjectCollect {


    /**
     * 项目ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String projectId;

    /**
     * 项目编号/项目代码
     */
    private String projectNo;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 项目所在镇（街）ID
     */
    private String projectTownId;

    /**
     * 项目所在镇（街）
     */
    private String projectTown;

    /**
     * 项目详细地址
     */
    private String projectAddress;

    /**
     * 建设单位ID
     */
    private String consUnitId;

    /**
     * 项目类型
     */
    private String dataType;

    /**
     * 是否装配式建筑
     */
    private String isBuild;

    /**
     * 宗地代码
     */
    private String landNo;

    /**
     * 行政区
     */
    private String xzq;

    /**
     * 项目坐落
     */
    private String xmzl;

    /**
     * 项目总投资（万元）
     */
    private Double xmztz;

    /**
     * 建设单位
     */
    private String consUnit;

    /**
     * 土地面积（平方米）
     */
    private Double tdmj;

    /**
     * 总建筑面积（平方米）
     */
    private Double zjzmj;

    /**
     * 计划开工日期
     */
    private Date jhkgrq;

    /**
     * 计划竣工日期
     */
    private Date jhjgrq;

    /**
     * 数据状态
     */
    private String dataState;

    /**
     * 土地用途
     */
    private String tdyt;

    /**
     * 建设性质
     */
    private String jsxz;

    /**
     * 建设类别
     */
    private String jslb;

    /**
     * 容积率
     */
    private Double rjl;

    /**
     * 绿地率
     */
    private Double ldl;

    /**
     * 建设规模及内容
     */
    private String jsgmjnr;

    /**
     * 项目状态 1.未介入 2.在建 3.停工 4.安全终止监督 5.质量竣工验收
     */
    private String projectState;

    /**
     * 是否重大项目 1.是 0.否
     */
    private String isMajorProject;

    /**
     * 数据最后修改时间
     */
    private Date updateTime;

    /**
     * 父项目ID
     */
    private String firstId;

    /**
     * 监督注册号
     */
    private String superviseNo;

    /**
     * 同步时间
     */
    private Date jkSyncTime;
}

