package com.jky.digital.data.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * @description:
 * @author: cq
 * @date: 2025/6/10 14:48
 */
@Data
@TableName("dgzjjzj_sup_jcjg_unqualified_reply_file")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "dgzjjzj_sup_jcjg_unqualified_reply_file 对象", description = "")
public class DigitalDgzjjzjSupJcjgUnqualifiedReplyFile {


    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 检测不合格数据主键
     */
    private String syNum;

    /**
     * 附件所属模板的模板名称
     */
    private String moduleFileName;

    /**
     * 附件名称
     */
    private String fileName;

    /**
     * 附件网络访问路径
     */
    private String fileUrl;

    /**
     * 数据创建时间
     */
    private Date createTime;

    /**
     * 数据更新时间
     */
    private Date updateTime;

    /**
     * 是否有效:0有效,1无效
     */
    private Integer invalid;

    /**
     * 数据同步时间
     */
    private Date jkSyncTime;
}

