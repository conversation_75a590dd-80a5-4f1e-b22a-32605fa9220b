package com.jky.digital.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jky.digital.data.entity.DigitalSysDict;
import com.jky.digital.data.entity.DigitalSysDictItem;
import com.jky.digital.data.mapper.DigitalSysDictItemMapper;
import com.jky.digital.data.mapper.DigitalSysDictMapper;
import com.jky.digital.service.ICommonService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @description:
 * @author: cq
 * @date: 2025/6/12 11:43
 */
@Service
@AllArgsConstructor
@Slf4j
public class CommonServiceImpl implements ICommonService {

    private final DigitalSysDictMapper digitalSysDictMapper;

    private final DigitalSysDictItemMapper digitalSysDictItemMapper;

    @Override
    public List<DigitalSysDictItem> queryDictList(String code) {

        DigitalSysDict digitalSysDict = digitalSysDictMapper.selectOne(new LambdaQueryWrapper<DigitalSysDict>().eq(DigitalSysDict::getDictCode, code));

        List<DigitalSysDictItem> dictItems = digitalSysDictItemMapper.selectList(new LambdaQueryWrapper<DigitalSysDictItem>()
                .eq(DigitalSysDictItem::getDictId, digitalSysDict.getId()).orderByAsc(DigitalSysDictItem::getCreateTime));

        return dictItems;
    }

}

