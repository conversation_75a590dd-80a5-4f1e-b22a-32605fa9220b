package com.jky.digital.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jky.digital.data.entity.*;
import com.jky.digital.data.mapper.*;
import com.jky.digital.data.vo.DivisionalProgressVo;
import com.jky.digital.data.vo.MonomerProgressVo;
import com.jky.digital.data.vo.ProjectProgressVo;
import com.jky.digital.multiuser.entity.DigitalSmzProjectInfo;
import com.jky.digital.multiuser.mapper.DigitalSmzProjectInfoMapper;
import com.jky.digital.service.IProjectService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.constant.CommonConstant;
import org.jeecg.common.system.vo.LoginUser;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @description:
 * @author: cq
 * @date: 2025/6/12 16:03
 */

@Service
@AllArgsConstructor
@Slf4j
public class ProjectServiceImpl implements IProjectService {

    private final DigitalSysDictItemMapper digitalSysDictItemMapper;

    private final DigitalAqjdConsSiteSubMapper digitalAqjdConsSiteSubMapper;

    private final DigitalAqjgConsSiteMapper digitalAqjgConsSiteMapper;

    private final DigitalSmzProjectInfoMapper digitalSmzProjectInfoMapper;

    private final DigitalPdProjectCollectMapper digitalPdProjectCollectMapper;

    private final DigitalPdMonomerCollectMapper digitalPdMonomerCollectMapper;

    private final DigitalPdMonomerDetailCollectMapper digitalPdMonomerDetailCollectMapper;

    private final DigitalDgdocArchivesInstanceMapper digitalDgdocArchivesInstanceMapper;

    @Override
    public Map<String, List<Object>> queryTownsInfoList() {
        Map<String, List<Object>> result = new HashMap<>();

        LoginUser user = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        Map<String, List<DigitalAqjgConsSite>> groupByTownName = new HashMap<>();
        Integer entryUserType = user.getEntryUserType();
        List<DigitalSmzProjectInfo> digitalSmzProjectInfos = this.queryProjectList(user.getId(), entryUserType);

        Set<String> projectIds = digitalSmzProjectInfos.stream().map(DigitalSmzProjectInfo::getMonomerProjectId).collect(Collectors.toSet());
        if (CollectionUtil.isNotEmpty(projectIds)) {

            List<DigitalPdProjectCollect> projectCollects = digitalPdProjectCollectMapper.selectList(new LambdaQueryWrapper<DigitalPdProjectCollect>().in(
                    DigitalPdProjectCollect::getProjectId, projectIds, projectIds.size())
            );

            Map<String, List<DigitalPdProjectCollect>> projectTownGroup = projectCollects.stream().collect(Collectors.groupingBy(DigitalPdProjectCollect::getProjectTown));

            if (CommonConstant.USER_TYPE_VIP_APP.equals(entryUserType)) {
//                result.putAll((Map<? extends String, ? extends List<Object>>) projectTownGroup);
            } else if (CommonConstant.USER_TYPE_ZTF_WX.equals(entryUserType)) {
                Map<String, Map<String, List<DigitalAqjgConsSite>>> m;
                List<DigitalAqjgConsSite> aqjgConsSites = digitalAqjgConsSiteMapper.selectList(
                        new LambdaQueryWrapper<DigitalAqjgConsSite>()
                                .in(DigitalAqjgConsSite::getProjectId, projectIds, projectIds.size()));

                Map<String, List<DigitalAqjgConsSite>> townshipGroup = aqjgConsSites.stream()
                        .collect(Collectors.groupingBy(DigitalAqjgConsSite::getTownshipName));
//                result.putAll((Map<? extends String, ? extends List<Object>>) townshipGroup);
//                if (CollectionUtil.isNotEmpty(aqjgConsSites)) {
//                    List<DigitalAqjdConsSiteSub> digitalAqjdConsSiteSubs = digitalAqjdConsSiteSubMapper.selectList(new LambdaQueryWrapper<DigitalAqjdConsSiteSub>()
//                            .in(DigitalAqjdConsSiteSub::getConsSiteId, aqjgConsSites.stream()
//                                    .map(DigitalAqjgConsSite::getId)
//                                    .collect(Collectors.toSet())));
//                }

            } else {

            }

        }

        return result;
    }

    private List<DigitalSmzProjectInfo> queryProjectList(String userId, Integer entryUserType) {
        ProjectServiceImpl bean = SpringUtil.getBean(ProjectServiceImpl.class);
        if (CommonConstant.USER_TYPE_VIP_APP.equals(entryUserType)) {
            return bean.queryZtfProjectList(userId);
        } else if (CommonConstant.USER_TYPE_ZTF_WX.equals(entryUserType)) {
            return bean.queryVipProjectList(userId);
        } else {
            return bean.queryZtfProjectList(userId);
        }
    }

    @DS("userZtf")
    @Override
    public List<DigitalSmzProjectInfo> queryZtfProjectList(String userId) {
        return digitalSmzProjectInfoMapper.queryByUserPhoneAndId(userId);
    }

    @DS("userVip")
    @Override
    public List<DigitalSmzProjectInfo> queryVipProjectList(String userId) {
        return digitalSmzProjectInfoMapper.queryByUserPhoneAndId(userId);
    }

    @Override
    public List<MonomerProgressVo> monomerProgress(String businessId) {
        List<MonomerProgressVo> ret = new ArrayList<>();

        List<DigitalPdMonomerCollect> collects = digitalPdMonomerCollectMapper.selectList(new LambdaQueryWrapper<DigitalPdMonomerCollect>()
                .eq(DigitalPdMonomerCollect::getProjectId, businessId));
        List<String> monomerIds = collects.stream().map(DigitalPdMonomerCollect::getMonomerId).collect(Collectors.toList());
        List<DigitalPdMonomerDetailCollect> details = digitalPdMonomerDetailCollectMapper.selectList(new LambdaQueryWrapper<DigitalPdMonomerDetailCollect>()
                .in(DigitalPdMonomerDetailCollect::getMonomerId, monomerIds));

        // 遍历collects并将monomerName添加到ret中
        for (DigitalPdMonomerCollect collect : collects) {
            MonomerProgressVo vo = new MonomerProgressVo();
            vo.setMonomerName(collect.getMonomerName());

            // 根据monomerId找到对应的detail
            Optional<DigitalPdMonomerDetailCollect> detailOptional = details.stream()
                    .filter(detail -> detail.getMonomerId().equals(collect.getMonomerId()))
                    .findFirst();

            if (detailOptional.isPresent()) {
                DigitalPdMonomerDetailCollect detail = detailOptional.get();

                // 将startdate和enddate转换为LocalDateTime
                Date startDateTime = detail.getStartdate();
                Date endDateTime = detail.getEnddate();
                // 转换为LocalDate（忽略时区和时间部分，只计算日期差）
                LocalDate localDate1 = startDateTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
                LocalDate localDate2 = endDateTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();

                // 格式化日期为yyyy-MM-dd
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
                String formattedStartDate = localDate1.format(formatter);
                String formattedEndDate = localDate2.format(formatter);

                // 设置startDate和endDate
                vo.setStartDate(formattedStartDate);
                vo.setEndDate(formattedEndDate);

                // 计算constructionDays
                long constructionDays = Math.abs(localDate2.toEpochDay() - localDate1.toEpochDay());
                vo.setConstructionDays(constructionDays);
                vo.setMonomerId(detail.getMonomerId());
            }

            ret.add(vo);
        }

        return ret;
    }

    @Override
    public List<DivisionalProgressVo> divisionalProgress(String monomerId) {
        List<DivisionalProgressVo> ret = new ArrayList<>();
        List<DigitalDgdocArchivesInstance> list = digitalDgdocArchivesInstanceMapper.selectList(
                new LambdaQueryWrapper<DigitalDgdocArchivesInstance>()
                        .eq(DigitalDgdocArchivesInstance::getStatus, "0")
                        .eq(DigitalDgdocArchivesInstance::getArchivesType, "1")
                        .eq(DigitalDgdocArchivesInstance::getProjectMonomerId, monomerId)
                        .in(DigitalDgdocArchivesInstance::getParentId, Arrays.asList("A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M"))
        );

        for (DigitalDgdocArchivesInstance instance : list) {
            DivisionalProgressVo vo = new DivisionalProgressVo();
            BeanUtils.copyProperties(instance, vo);
            ret.add(vo);
        }

        // 找到所有父节点（archives_no包含"01"）
        List<DivisionalProgressVo> roots = ret.stream()
                .filter(vo -> vo.getArchivesNo() != null && vo.getArchivesNo().contains("01"))
                .collect(Collectors.toList());

        // 组装children
        for (DivisionalProgressVo root : roots) {
            List<DivisionalProgressVo> children = ret.stream()
                    .filter(vo -> root.getParentId().equals(vo.getParentId()) && !vo.getArchivesNo().contains("01"))
                    .collect(Collectors.toList());
            root.setChildren(children);
        }

        /**
         * 整个分部的全部上传就完成，部分上传了就是施工中，一个都没上传就待施工
         */
        for (DivisionalProgressVo vo : ret) {
            if (CollectionUtil.isEmpty(vo.getChildren())) {
                vo.setConstructionStatus(vo.getUploadState());
            } else {
                boolean allZero = "0".equals(vo.getUploadState());
                boolean allTwo = "2".equals(vo.getUploadState());
                for (DivisionalProgressVo child : vo.getChildren()) {
                    child.setConstructionStatus(child.getUploadState());
                    if (!"0".equals(child.getUploadState())) {
                        allZero = false;
                    }
                    if (!"2".equals(child.getUploadState())) {
                        allTwo = false;
                    }
                }
                if (allZero) {
                    vo.setConstructionStatus("0");
                } else if (allTwo) {
                    vo.setConstructionStatus("2");
                } else {
                    vo.setConstructionStatus("1");
                }
            }
        }

        return roots;
    }

    @Override
    public List<ProjectProgressVo> projectProgress(String projectId) {
//        List<ProjectProgressVo> ret = new ArrayList<>();
//
//        List<DigitalPdProjectCollect> collects = digitalPdProjectCollectMapper.selectList(new LambdaQueryWrapper<DigitalPdProjectCollect>().in(
//                DigitalPdProjectCollect::getProjectId, projectId)
//        );
//
//
//        // 遍历collects并将monomerName添加到ret中
//        for (DigitalPdProjectCollect collect : collects) {
//            ProjectProgressVo vo = new ProjectProgressVo();
//            vo.setProjectName(collect.getProjectName());
//
//            // 根据monomerId找到对应的detail
//            Optional<DigitalPdMonomerDetailCollect> detailOptional = details.stream()
//                    .filter(detail -> detail.getMonomerId().equals(collect.getMonomerId()))
//                    .findFirst();
//
//            if (detailOptional.isPresent()) {
//                DigitalPdMonomerDetailCollect detail = detailOptional.get();
//
//                // 将startdate和enddate转换为LocalDateTime
//                Date startDateTime = detail.getStartdate();
//                Date endDateTime = detail.getEnddate();
//                // 转换为LocalDate（忽略时区和时间部分，只计算日期差）
//                LocalDate localDate1 = startDateTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
//                LocalDate localDate2 = endDateTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
//
//                // 格式化日期为yyyy-MM-dd
//                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
//                String formattedStartDate = localDate1.format(formatter);
//                String formattedEndDate = localDate2.format(formatter);
//
//                // 设置startDate和endDate
//                vo.setStartDate(formattedStartDate);
//                vo.setEndDate(formattedEndDate);
//
//                // 计算constructionDays
//                long constructionDays = Math.abs(localDate2.toEpochDay() - localDate1.toEpochDay());
//                vo.setConstructionDays(constructionDays);
//                vo.setMonomerId(detail.getMonomerId());
//            }
//
//            ret.add(vo);
//        }
//
//        return ret;
        return null;
    }
}

