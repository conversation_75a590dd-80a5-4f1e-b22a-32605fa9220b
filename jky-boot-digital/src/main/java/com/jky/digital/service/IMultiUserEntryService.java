package com.jky.digital.service;

import com.jky.digital.multiuser.DigitalVipLoginInfo;
import com.jky.digital.multiuser.domain.MultiEntryLoginDto;
import com.jky.digital.multiuser.domain.MultiUserEntryRequestBo;
import com.jky.digital.multiuser.domain.dto.VipEntryLoginDto;

import javax.servlet.http.HttpServletRequest;

/**
 * @description:
 * @author: cq
 * @date: 2025/6/10 16:07
 */

public interface IMultiUserEntryService {

    MultiEntryLoginDto entry(MultiUserEntryRequestBo bo) throws Exception;

    VipEntryLoginDto vipEntry(HttpServletRequest request, DigitalVipLoginInfo bo) throws Exception;
}
