package com.jky.digital.service.impl;

import com.jky.digital.constants.MultiUserEntryConstants;
import com.jky.digital.multiuser.DigitalVipLoginInfo;
import com.jky.digital.multiuser.domain.MultiEntryLoginDto;
import com.jky.digital.multiuser.domain.MultiUserEntryRequestBo;
import com.jky.digital.multiuser.domain.dto.VipEntryLoginDto;
import com.jky.digital.service.IMultiUserEntryService;
import com.jky.digital.service.IMultiUserService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.constant.CommonConstant;
import org.jeecg.common.system.util.JwtUtil;
import org.jeecg.common.util.RedisUtil;
import org.springframework.stereotype.Service;

import javax.management.ServiceNotFoundException;
import javax.servlet.http.HttpServletRequest;

/**
 * @description:
 * @author: cq
 * @date: 2025/6/10 16:07
 */

@Service
@AllArgsConstructor
@Slf4j
public class MultiUserEntryServiceImpl implements IMultiUserEntryService {

    private final RedisUtil redisUtil;

    private final IMultiUserService multiUserService;

    /**
     * client_id 和请求头的vipApiKey是固定值，用于识别是从城建app进来数字工地H5的标志
     */
    private final String vipApiKey = "vipH5-5Uyy1ydhpPTLPN82f";
    private final String clientId = "viph5_202506ef052eaff2ce4d5fb004edb";


    @Override
    public MultiEntryLoginDto entry(MultiUserEntryRequestBo bo) throws Exception {
        MultiEntryLoginDto dto = new MultiEntryLoginDto();
        if (MultiUserEntryConstants.USER_TYPE_ZTF_WX == bo.getEntry()) {
           /* String response = HttpUtil.get("https://dgfcjy.dg.cn/ibasem/api/DI/zj/Person/info?access_token=" + bo.getToken());
            WechatLoginDto wechatLoginDto = JSONUtil.toBean(response, WechatLoginDto.class);
            if (wechatLoginDto.getCode() != 200) {
                throw new Exception(wechatLoginDto.getMessage());
            }
            dto.setMobile(wechatLoginDto.getData().getMobile());
            dto.setName(wechatLoginDto.getData().getRealName());

            // 住建用户没有关系到项目的，弹窗无权限访问
            Boolean b = multiUserService.checkZtfUserProject(dto.getMobile());
            dto.setHasPermission(b);*/

            dto.setMobile(bo.getToken());
            dto.setHasPermission(true);
        } else if (MultiUserEntryConstants.USER_TYPE_VIP_APP == bo.getEntry()) {
            /**
             * TODO 功能用户对接
             */

        } else if (MultiUserEntryConstants.USER_TYPE_TEST == bo.getEntry()) {
            dto.setMobile(bo.getToken());
        } else {
            throw new ServiceNotFoundException("未定义的登录用户！");
        }
        com.jky.digital.multiuser.entity.DigitalSmzUser sysUser = multiUserService.queryMultiUserInfo(dto, bo.getEntry());

        String token = JwtUtil.sign(sysUser.getPhone() + "_" + sysUser.getId(), sysUser.getId());
        // 设置token缓存有效时间
        redisUtil.set(CommonConstant.PREFIX_USER_TOKEN + token, token);
        redisUtil.set(CommonConstant.MULTI_USER_NAME_DB + sysUser.getPhone() + "_" + sysUser.getId(), bo.getEntry());
        redisUtil.expire(CommonConstant.PREFIX_USER_TOKEN + token, JwtUtil.EXPIRE_TIME * 2 / 1000);
        dto.setDigitalSmzUser(sysUser);
        dto.setToken(token);

        return dto;
    }

    @Override
    public VipEntryLoginDto vipEntry(HttpServletRequest request, DigitalVipLoginInfo bo) throws Exception {
        VipEntryLoginDto ret = new VipEntryLoginDto();
        String providedApiKey = request.getHeader("vipApiKey");

        if (!vipApiKey.equals(providedApiKey)) {
            throw new Exception("无效的请求头");
        }

        //根据手机号判断用户是否存在
        MultiEntryLoginDto dto = new MultiEntryLoginDto();
        dto.setMobile(bo.getPhone());
        com.jky.digital.multiuser.entity.DigitalSmzUser sysUser = multiUserService.queryMultiUserInfo(dto, MultiUserEntryConstants.USER_TYPE_VIP_APP);
        if (sysUser == null || !sysUser.getPhone().equals(bo.getPhone())) {
            throw new Exception("用户不存在");
        }

        String baseUrl = "https://vipgd.dgjky.com/";
        String token = JwtUtil.sign(sysUser.getPhone() + "_" + sysUser.getId(), sysUser.getId());
        // 设置token缓存有效时间
        redisUtil.set(CommonConstant.PREFIX_USER_TOKEN + token, token);
        redisUtil.set(CommonConstant.MULTI_USER_NAME_DB + bo.getPhone() + "_" + sysUser.getId(), MultiUserEntryConstants.USER_TYPE_VIP_APP);
        redisUtil.expire(CommonConstant.PREFIX_USER_TOKEN + token, JwtUtil.EXPIRE_TIME * 2 / 1000);

        ret.setUrl(baseUrl + bo.getResponsePageType() + "?client_id=" + clientId + "&token=" + token);
        ret.setDigitalSmzUser(sysUser);
        return ret;
    }
}

