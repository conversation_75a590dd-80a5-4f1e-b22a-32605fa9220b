package com.jky.digital.service;

import com.jky.digital.multiuser.domain.MultiEntryLoginDto;
import com.jky.digital.multiuser.domain.MultiUserEntryRequestBo;
import com.jky.digital.multiuser.entity.DigitalSmzUser;

/**
 * @description:
 * @author: cq
 * @date: 2025/6/10 17:20
 */

public interface IMultiUserService {

    com.jky.digital.multiuser.entity.DigitalSmzUser queryMultiUserInfo(MultiEntryLoginDto dto, Integer entry) throws Exception;

    DigitalSmzUser chooseByMultiDB(String username, int userType);

    /**
     * 校验住建用户是否有项目
     */
    Boolean checkZtfUserProject(String phone);

}

