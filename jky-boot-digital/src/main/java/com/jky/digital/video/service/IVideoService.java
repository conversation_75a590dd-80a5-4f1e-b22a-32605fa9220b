package com.jky.digital.video.service;

import com.jky.digital.video.domain.entity.TbFacilityItem;

import java.util.List;

/**
 * @Author: lpg
 * @Date: 2025/06/24
 * @Description:
 */
public interface IVideoService {
    /**
     * 查询设备列表
     *
     * @return
     */
    List<TbFacilityItem> queryDeviceList(String projectId);

    /**
     * 播放设备
     *
     * @param deviceId
     * @return
     */
    Object startPlay(String deviceId);

    /**
     * 设备控制-放大缩小，聚焦
     *
     * @param deviceId 设备编号
     * @param cmd      控制命令类型 放大：1 缩小：2光圈放大: 3 光圈缩小：4聚焦远：5聚焦近：6
     * @param speed    调整速度
     * @return
     */
    Object controlZoom(String deviceId, String cmd, String speed);

    /**
     * 设备控制-方向旋转
     *
     * @param deviceId 设备编号
     * @param cmd      控制命令类型 控制命令类型 向左：1向右：2向上：3向下：4左上: 5左下: 6右上: 7右下: 8
     * @param speed    调整速度
     * @return
     */
    Object controlDirect(String deviceId, String cmd, String speed);
}
