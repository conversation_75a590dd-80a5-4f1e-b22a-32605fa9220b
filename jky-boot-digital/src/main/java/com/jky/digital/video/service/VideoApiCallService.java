package com.jky.digital.video.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jky.digital.video.util.SignatureHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.util.HashMap;
import java.util.Map;
import java.util.Random;

/*** 调用demo */
@Slf4j
@Component
public class VideoApiCallService {
    /*** get请求 * @throws Exception */
    public void testGet() throws Exception {
        String url = "https://dg.tianyvideo.com:12345/device/api/deviceInfo/getManagedPlatList";
        Map<String, Object> paramMap = new HashMap<>();
//        paramMap.put("streamStatus", false); //拼接url
        UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(url);
        for (Map.Entry<String, Object> e : paramMap.entrySet()) {
            builder.queryParam(e.getKey(), e.getValue());
        }
        String realUrl = builder.build().toString(); //指定header
        HttpHeaders headers = SignatureHelper.generateHeader(paramMap);
        HttpEntity<JSONObject> httpEntity = new HttpEntity<>(headers);
        RestTemplate restTemplate = new RestTemplate();
        ResponseEntity<Object> exchange = restTemplate.exchange(realUrl, HttpMethod.GET, httpEntity, Object.class);
        Object obj = exchange.getBody();
        if (obj != null) {
            log.info(JSON.toJSONString(obj));
        }
    }

    /*** post请求 * form‐data或param传参 * @throws Exception */
    public void testPost() throws Exception {
        String url2 = "http://localhost:8080/test2";
        Map<String, Object> paramMap2 = new HashMap<>();
        paramMap2.put("faceGroupName", "test");
        paramMap2.put("description", "test");
        HttpHeaders headers2 = SignatureHelper.generateHeader(paramMap2);
        HttpEntity httpEntity2 = new HttpEntity<>(paramMap2, headers2);
        RestTemplate restTemplate = new RestTemplate();
        Object obj2 = restTemplate.postForObject(url2, httpEntity2, Object.class);
        if (obj2 != null) {
            log.info(JSON.toJSONString(obj2));
        }
    }

    /*** post请求 * Content‐Type类型为application/json * @throws Exception */
    public void testBodyPost() throws Exception {
        String url = "http://localhost:8080/test3";
        Map<String, Object> paramMap2 = new HashMap<>();
        paramMap2.put("type", 4);
        paramMap2.put("applyType", 1);
        //指定header和传参
        Map<String, Object> headerParam = new HashMap<>();
        headerParam.put("jsonBody", JSON.toJSONString(paramMap2));
        HttpHeaders headers2 = SignatureHelper.generateHeader(headerParam);
        HttpEntity httpEntity2 = new HttpEntity<>(paramMap2, headers2);
        RestTemplate restTemplate = new RestTemplate();
        Object obj2 = restTemplate.postForObject(url, httpEntity2, Object.class);
        if (obj2 != null) {
            log.info(JSON.toJSONString(obj2));
        }
    }

    /**
     * 视频设备查询接口
     */
    public JSONObject devicceList() {
        String url = "https://dg.tianyvideo.com:12345/device/api/videoClient/deviceList";
        Map<String, Object> paramMap = new HashMap<>();
//        paramMap.put("streamStatus", false); //拼接url
        UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(url);
        for (Map.Entry<String, Object> e : paramMap.entrySet()) {
            builder.queryParam(e.getKey(), e.getValue());
        }
        String realUrl = builder.build().toString(); //指定header
        HttpHeaders headers = SignatureHelper.generateHeader(paramMap);
        HttpEntity<JSONObject> httpEntity = new HttpEntity<>(headers);
        RestTemplate restTemplate = new RestTemplate();
        ResponseEntity<Object> exchange = restTemplate.exchange(realUrl, HttpMethod.GET, httpEntity, Object.class);
        Object obj = exchange.getBody();

        if (obj != null) {
            String jsonresult = JSON.toJSONString(obj);
            log.info(jsonresult);
            return JSON.parseObject(jsonresult);
        }
        return null;
    }


    /**
     * 视频设备查询接口
     */
    public JSONObject devicceList2() {
        String url = "https://dg.tianyvideo.com:12345/device/api/deviceInfo/getDeviceList";
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("pageSize", "1000000"); //拼接url
        UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(url);
        for (Map.Entry<String, Object> e : paramMap.entrySet()) {
            builder.queryParam(e.getKey(), e.getValue());
        }
        String realUrl = builder.build().toString(); //指定header
        HttpHeaders headers = SignatureHelper.generateHeader(paramMap);
        HttpEntity<JSONObject> httpEntity = new HttpEntity<>(headers);
        RestTemplate restTemplate = new RestTemplate();
        ResponseEntity<Object> exchange = restTemplate.exchange(realUrl, HttpMethod.GET, httpEntity, Object.class);
        Object obj = exchange.getBody();

        if (obj != null) {
            String jsonresult = JSON.toJSONString(obj);
            log.info(jsonresult);
            return JSON.parseObject(jsonresult);
        }
        return null;
    }

    /**
     * 设备详情查询
     */
    public JSONObject detail2(String deviceId) {
        String url = "https://dg.tianyvideo.com:12345/device/api/deviceInfo/detail2/" + deviceId;
        Map<String, Object> paramMap = new HashMap<>();
//        paramMap.put("streamStatus", false); //拼接url
        UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(url);
        for (Map.Entry<String, Object> e : paramMap.entrySet()) {
            builder.queryParam(e.getKey(), e.getValue());
        }
        String realUrl = builder.build().toString(); //指定header
        HttpHeaders headers = SignatureHelper.generateHeader(paramMap);
        HttpEntity<JSONObject> httpEntity = new HttpEntity<>(headers);
        RestTemplate restTemplate = new RestTemplate();
        ResponseEntity<Object> exchange = restTemplate.exchange(realUrl, HttpMethod.GET, httpEntity, Object.class);
        Object obj = exchange.getBody();

        if (obj != null) {
            String jsonresult = JSON.toJSONString(obj);
            log.info(jsonresult);
            return JSON.parseObject(jsonresult);
        }
        return null;
    }

    /**
     * 查询项目列表
     *
     * @throws Exception
     */
    public void projectList() {
        String url = "https://dg.tianyvideo.com:12345/device/api/deviceInfo/getManagedPlatList";
        Map<String, Object> paramMap = new HashMap<>();
        UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(url);
        for (Map.Entry<String, Object> e : paramMap.entrySet()) {
            builder.queryParam(e.getKey(), e.getValue());
        }
        String realUrl = builder.build().toString(); //指定header
        HttpHeaders headers = SignatureHelper.generateHeader(paramMap);
        HttpEntity<JSONObject> httpEntity = new HttpEntity<>(headers);
        RestTemplate restTemplate = new RestTemplate();
        ResponseEntity<Object> exchange = restTemplate.exchange(realUrl, HttpMethod.GET, httpEntity, Object.class);
        Object obj = exchange.getBody();
        if (obj != null) {
            log.info(JSON.toJSONString(obj));
        }
    }

    /**
     * 查询实时视频播放列表
     *
     * @throws Exception
     */
    public JSONObject startPlay(String deviceId) {
        String url = "https://dg.tianyvideo.com:12345/device/api/videoClient/startPlay";
        Random random = new Random();
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("deviceId", deviceId);
        paramMap.put("tag", String.format("%06d", random.nextInt(999999)));
        paramMap.put("ifDealUrl", true);
        UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(url);
        for (Map.Entry<String, Object> e : paramMap.entrySet()) {
            builder.queryParam(e.getKey(), e.getValue());
        }
        String realUrl = builder.build().toString(); //指定header
        HttpHeaders headers = SignatureHelper.generateHeader(paramMap);
        HttpEntity<JSONObject> httpEntity = new HttpEntity<>(headers);
        RestTemplate restTemplate = new RestTemplate();
        ResponseEntity<Object> exchange = restTemplate.exchange(realUrl, HttpMethod.GET, httpEntity, Object.class);
        Object obj = exchange.getBody();
        if (obj != null) {
            String jsonresult = JSON.toJSONString(obj);
            log.info(jsonresult);
            JSONObject jsonObject = JSON.parseObject(jsonresult);
            if (jsonObject.getInteger("status") == 200) {
                return jsonObject.getJSONObject("data");
            } else {
                throw new RuntimeException(jsonObject.getString("message"));
            }
        }
        return null;
    }

    /**
     * 设备控制-方向旋转
     *
     * @throws Exception
     */
    public JSONObject controlDirect(String deviceId, String cmd, String speed) {
        String url = "https://dg.tianyvideo.com:12345/device/api/videoClient/controlDirect";
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("deviceId", deviceId);
        paramMap.put("cmd", cmd);
        paramMap.put("speed", speed);
        UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(url);
        for (Map.Entry<String, Object> e : paramMap.entrySet()) {
            builder.queryParam(e.getKey(), e.getValue());
        }
        String realUrl = builder.build().toString(); //指定header
        HttpHeaders headers = SignatureHelper.generateHeader(paramMap);
        HttpEntity<JSONObject> httpEntity = new HttpEntity<>(headers);
        RestTemplate restTemplate = new RestTemplate();
        ResponseEntity<Object> exchange = restTemplate.exchange(realUrl, HttpMethod.GET, httpEntity, Object.class);
        Object obj = exchange.getBody();
        if (obj != null) {
            String jsonresult = JSON.toJSONString(obj);
            log.info(jsonresult);
            JSONObject jsonObject = JSON.parseObject(jsonresult);
            if (jsonObject.getInteger("status") == 200) {
                return jsonObject.getJSONObject("data");
            } else {
                throw new RuntimeException(jsonObject.getString("message"));
            }
        }
        return null;
    }

    /**
     * 设备控制-放大缩小，聚焦
     *
     * @throws Exception
     */
    public JSONObject controlZoom(String deviceId, String cmd, String speed) {
        String url = "https://dg.tianyvideo.com:12345/device/api/videoClient/controlZoom";
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("deviceId", deviceId);
        paramMap.put("cmd", cmd);
        paramMap.put("speed", speed);
        UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(url);
        for (Map.Entry<String, Object> e : paramMap.entrySet()) {
            builder.queryParam(e.getKey(), e.getValue());
        }
        String realUrl = builder.build().toString(); //指定header
        HttpHeaders headers = SignatureHelper.generateHeader(paramMap);
        HttpEntity<JSONObject> httpEntity = new HttpEntity<>(headers);
        RestTemplate restTemplate = new RestTemplate();
        ResponseEntity<Object> exchange = restTemplate.exchange(realUrl, HttpMethod.GET, httpEntity, Object.class);
        Object obj = exchange.getBody();
        if (obj != null) {
            String jsonresult = JSON.toJSONString(obj);
            log.info(jsonresult);
            JSONObject jsonObject = JSON.parseObject(jsonresult);
            if (jsonObject.getInteger("status") == 200) {
                return jsonObject.getJSONObject("data");
            } else {
                throw new RuntimeException(jsonObject.getString("message"));
            }
        }
        return null;
    }

    /**
     * 设备控制-设备在线状态查询
     *
     * @throws Exception
     */
    public JSONObject deviceStatus(String deviceId) {
        String url = "https://dg.tianyvideo.com:12345/device/api/videoClient/deviceStatus";
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("deviceId", deviceId);
        UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(url);
        for (Map.Entry<String, Object> e : paramMap.entrySet()) {
            builder.queryParam(e.getKey(), e.getValue());
        }
        String realUrl = builder.build().toString(); //指定header
        HttpHeaders headers = SignatureHelper.generateHeader(paramMap);
        HttpEntity<JSONObject> httpEntity = new HttpEntity<>(headers);
        RestTemplate restTemplate = new RestTemplate();
        ResponseEntity<Object> exchange = restTemplate.exchange(realUrl, HttpMethod.GET, httpEntity, Object.class);
        Object obj = exchange.getBody();
        if (obj != null) {
            String jsonresult = JSON.toJSONString(obj);
            log.info(jsonresult);
            JSONObject jsonObject = JSON.parseObject(jsonresult);
            if (jsonObject.getInteger("status") == 200) {
                JSONArray arrData = jsonObject.getJSONArray("data");
                return arrData.getJSONObject(0);
            } else {
                return null;
            }
        }
        return null;
    }

    public static void main(String[] args) {

        //天影平台项目编码映射表
        //1.瑧湾汇项目  44190100461180002588
        //2.天空之城项目  44192300461180006687、44191800461180006908
        try {
            VideoApiCallService demo = new VideoApiCallService();
//            demo.devicceList();
            demo.startPlay("44190100461320003077");
//            demo.deviceStatus("44190100461320003081");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}