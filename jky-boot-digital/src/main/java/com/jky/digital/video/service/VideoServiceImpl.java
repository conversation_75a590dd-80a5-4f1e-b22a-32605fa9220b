package com.jky.digital.video.service;

import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.jky.digital.video.domain.entity.TbFacilityItem;
import com.jky.digital.video.mapper.VipTbFacilityItemMapper;
import com.jky.digital.video.mapper.ZtfTbFacilityItemMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: lpg
 * @Date: 2025/06/24
 * @Description:
 */
@RequiredArgsConstructor
@Service
public class VideoServiceImpl implements IVideoService {
    private final VipTbFacilityItemMapper vipTbFacilityItemMapper;
    private final ZtfTbFacilityItemMapper ztfTbFacilityItemMapper;
    private final VideoApiCallService videoApiCallService;

    @Override
    public Object controlDirect(String deviceId, String cmd, String speed) {
        return videoApiCallService.controlDirect(deviceId, cmd, speed);
    }

    @Override
    public Object controlZoom(String deviceId, String cmd, String speed) {
        return videoApiCallService.controlZoom(deviceId, cmd, speed);
    }

    @Override
    public Object startPlay(String deviceId) {
        return videoApiCallService.startPlay(deviceId);
    }

    @Override
    public List<TbFacilityItem> queryDeviceList(String projectId) {
        List<TbFacilityItem> vipFacilityItems = vipTbFacilityItemMapper.selectList(Wrappers.<TbFacilityItem>lambdaQuery()
                .eq(TbFacilityItem::getProjectId, projectId)
                .orderByDesc(TbFacilityItem::getCreateTime));
        List<TbFacilityItem> ztfFacilityItems = ztfTbFacilityItemMapper.selectList(Wrappers.<TbFacilityItem>lambdaQuery()
                .eq(TbFacilityItem::getProjectId, projectId)
                .orderByDesc(TbFacilityItem::getCreateTime));
        vipFacilityItems.addAll(ztfFacilityItems);
        return vipFacilityItems.stream()
                .filter(item -> StringUtils.isNotBlank(item.getDeviceSerial())
                        && StringUtils.isNotBlank(item.getLable()) && (item.getLable().equals("执法点")) || item.getLable().equals("监控点"))
                .collect(Collectors.toMap(
                        TbFacilityItem::getDeviceSerial,
                        item -> item,
                        (item1, item2) -> item1.getCreateTime().after(item2.getCreateTime()) ? item1 : item2
                ))
                .values()
                .stream()
                .sorted(Comparator.comparing(TbFacilityItem::getCreateTime).reversed())
                .collect(Collectors.toList());


    }
}
