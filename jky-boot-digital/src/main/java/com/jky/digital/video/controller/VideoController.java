package com.jky.digital.video.controller;

import com.jky.digital.video.domain.entity.TbFacilityItem;
import com.jky.digital.video.service.IVideoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;
import java.util.List;

@RestController
@RequestMapping("/video")
@Slf4j
@RequiredArgsConstructor
@Api(value = "视频接口", tags = {"视频接口"})
public class VideoController {

    private final IVideoService iVideoService;

    /**
     * 查询设备列表
     */
    @ApiModelProperty("查询设备列表")
    @GetMapping("/devicceList/{projectId}")
    private Result<List<TbFacilityItem>> queryDeviceList(@NotNull(message = "项目ID不能为空") @PathVariable String projectId) {
        return Result.OK(iVideoService.queryDeviceList(projectId));
    }

    /**
     * 播放设备
     */
    @ApiModelProperty("播放设备")
    @GetMapping("/startPlay/{deviceId}")
    private Result<Object> startPlay(@ApiParam("设备ID") @PathVariable String deviceId) {
        return Result.OK(iVideoService.startPlay(deviceId));
    }

    /**
     * 设备控制-放大缩小，聚焦
     */
    @ApiModelProperty("设备控制-放大缩小，聚焦")
    @GetMapping("/controlZoom")
    private Result<Object> controlZoom(@ApiParam("设备ID") @RequestParam String deviceId,
                                       @ApiParam("控制命令类型 放大：1 缩小：2光圈放大: 3 光圈缩小：4聚焦远：5聚焦近：6") @RequestParam String cmd,
                                       @ApiParam("调整速度") @RequestParam(required = false, defaultValue = "3") String speed) {
        return Result.OK(iVideoService.controlZoom(deviceId, cmd, speed));
    }

    /**
     * 设备控制-方向旋转
     */
    @ApiModelProperty("设备控制-方向旋转")
    @GetMapping("/controlDirect")
    private Result<Object> controlDirect(@ApiParam("设备ID") @RequestParam String deviceId,
                                         @ApiParam("控制命令类型 控制命令类型 向左：1向右：2向上：3向下：4左上: 5左下: 6右上: 7右下: 8") @RequestParam String cmd,
                                         @ApiParam("调整速度") @RequestParam(required = false, defaultValue = "3") String speed) {
        return Result.OK(iVideoService.controlDirect(deviceId, cmd, speed));
    }
}
