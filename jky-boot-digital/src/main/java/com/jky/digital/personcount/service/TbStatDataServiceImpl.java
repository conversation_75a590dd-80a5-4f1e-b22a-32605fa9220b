package com.jky.digital.personcount.service;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.jky.digital.constants.MultiUserEntryConstants;
import com.jky.digital.personcount.domain.entity.Gdh5VipgdTbStatData;
import com.jky.digital.personcount.domain.entity.Gdh5ZjgdTbStatData;
import com.jky.digital.personcount.domain.vo.PersonAttendanceVo;
import com.jky.digital.personcount.domain.vo.PersonInfoVo;
import com.jky.digital.personcount.mapper.Gdh5VipgdTbStatDataMapper;
import com.jky.digital.personcount.mapper.Gdh5ZjgdTbStatDataMapper;
import lombok.RequiredArgsConstructor;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.system.vo.LoginUser;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @Author: lpg
 * @Date: 2025/06/25
 * @Description:
 */
@RequiredArgsConstructor
@Service
public class TbStatDataServiceImpl implements ITbStatDataService {
    private final Gdh5VipgdTbStatDataMapper vipgdTbStatDataMapper;
    private final Gdh5ZjgdTbStatDataMapper zjgdTbStatDataMapper;

    @Override
    public List<PersonAttendanceVo> queryPersonAttendance(String projectId, String type, String dateType) {
        LoginUser user = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        List<PersonAttendanceVo> personAttendanceVoList = new ArrayList<>();
        if (ObjectUtil.isNull(user)) {
            user = new LoginUser();
            user.setEntryUserType(MultiUserEntryConstants.USER_TYPE_ZTF_WX);
        }
        if (MultiUserEntryConstants.USER_TYPE_ZTF_WX == user.getEntryUserType()) {
            Gdh5ZjgdTbStatData gdh5ZjgdTbStatData = zjgdTbStatDataMapper.selectOne(Wrappers.<Gdh5ZjgdTbStatData>lambdaQuery()
                    .eq(Gdh5ZjgdTbStatData::getProjectId, projectId)
                    .eq(Gdh5ZjgdTbStatData::getItemCode, "user-manage-builder-" + dateType)
                    .eq(Gdh5ZjgdTbStatData::getRemark, )
                    .eq(Gdh5ZjgdTbStatData::getItemCode, "user-jobcategory-count")
                    .orderByDesc(Gdh5ZjgdTbStatData::getCreateTime).last("limit 1"));
            /*
             *
             * */
        }

        return null;
    }

    @Override
    public PersonInfoVo queryPersonInfo(String projectId) {
        LoginUser user = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (ObjectUtil.isNull(user)) {
            user = new LoginUser();
            user.setEntryUserType(MultiUserEntryConstants.USER_TYPE_ZTF_WX);
        }
        PersonInfoVo vo = new PersonInfoVo();
        vo.setProjectId(projectId);

        if (MultiUserEntryConstants.USER_TYPE_ZTF_WX == user.getEntryUserType()) {
            Gdh5ZjgdTbStatData gdh5ZjgdTbStatData = zjgdTbStatDataMapper.selectOne(Wrappers.<Gdh5ZjgdTbStatData>lambdaQuery()
                    .eq(Gdh5ZjgdTbStatData::getProjectId, projectId)
                    .eq(Gdh5ZjgdTbStatData::getItemCode, "user-jobcategory-count")
                    .orderByDesc(Gdh5ZjgdTbStatData::getCreateTime).last("limit 1"));
            if (gdh5ZjgdTbStatData != null && StringUtils.isNotBlank(gdh5ZjgdTbStatData.getItemData())) {
                List<Map<String, Object>> itemList = new ArrayList<>();
                try {
                    itemList = new com.fasterxml.jackson.databind.ObjectMapper()
                            .readValue(gdh5ZjgdTbStatData.getItemData(), new com.fasterxml.jackson.core.type.TypeReference<List<Map<String, Object>>>() {
                            });
                    if (!itemList.isEmpty()) {
                        Map<String, Object> item = itemList.get(0);
                        vo.setInNum((Integer) item.getOrDefault("inNum", 0));
                        vo.setOutNum((Integer) item.getOrDefault("outNum", 0));
                        vo.setRegisterNum((Integer) item.getOrDefault("registerNum", 0));
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
        if (MultiUserEntryConstants.USER_TYPE_VIP_APP == user.getEntryUserType()) {
            Gdh5VipgdTbStatData vipgdTbStatData = vipgdTbStatDataMapper.selectOne(Wrappers.<Gdh5VipgdTbStatData>lambdaQuery()
                    .eq(Gdh5VipgdTbStatData::getProjectId, projectId)
                    .eq(Gdh5VipgdTbStatData::getItemCode, "user-jobcategory-count")
                    .orderByDesc(Gdh5VipgdTbStatData::getCreateTime).last("limit 1"));

            if (vipgdTbStatData != null && StringUtils.isNotBlank(vipgdTbStatData.getItemData())) {
                List<Map<String, Object>> itemList = new ArrayList<>();
                try {
                    itemList = new com.fasterxml.jackson.databind.ObjectMapper()
                            .readValue(vipgdTbStatData.getItemData(), new com.fasterxml.jackson.core.type.TypeReference<List<Map<String, Object>>>() {
                            });
                    if (!itemList.isEmpty()) {
                        Map<String, Object> item = itemList.get(0);
                        vo.setInNum((Integer) item.getOrDefault("inNum", 0));
                        vo.setOutNum((Integer) item.getOrDefault("outNum", 0));
                        vo.setRegisterNum((Integer) item.getOrDefault("registerNum", 0));
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
        return vo;
    }
}
