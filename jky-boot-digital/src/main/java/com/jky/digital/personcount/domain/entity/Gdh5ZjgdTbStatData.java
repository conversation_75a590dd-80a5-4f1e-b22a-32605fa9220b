package com.jky.digital.personcount.domain.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * @TableName view_gdh5_zjgd_tb_stat_data
 */
@TableName(value = "view_gdh5_zjgd_tb_stat_data")
@Data
public class Gdh5ZjgdTbStatData {
    /**
     *
     */
    private Long id;

    /**
     * 编码
     */
    private String itemCode;

    /**
     * 项目id
     */
    private String projectId;

    /**
     * 数据
     */
    private String itemData;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     *
     */
    private Date jkSyncTime;

    /**
     *
     */
    private String remark;
}